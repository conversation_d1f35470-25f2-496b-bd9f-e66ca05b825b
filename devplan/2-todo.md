# MCP Copilot - macOS 原生客户端开发计划

## 项目概述

本文档概述了 **MCP Copilot** 的开发计划，这是一个 macOS 原生 SwiftUI 应用程序，作为 yamcp（Yet Another MCP）工作区的管理层。该应用程序能够同时管理和执行多个 MCP 服务器工作区，同时为 AI 客户端集成提供可流式传输的 HTTP/SSE 接口。

## 功能设计文档

### 1. 核心架构

```
┌─────────────────────────────────────────────────────────────┐
│                    SwiftUI 前端界面                         │
│  ┌─────────────────┐ ┌─────────────────┐ ┌───────────────┐ │
│  │   工作区列表    │ │   服务器配置    │ │   系统托盘    │ │
│  └─────────────────┘ └─────────────────┘ └───────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                Swift 进程管理器                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌───────────────┐ │
│  │  yamcp 进程     │ │  supergateway   │ │   健康监控    │ │
│  └─────────────────┘ └─────────────────┘ └───────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     服务层                                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌───────────────┐ │
│  │   HTTP/SSE      │ │   端口管理      │ │   配置写入    │ │
│  │     端点        │ │   (7700+)       │ │  (AI 客户端)  │ │
│  └─────────────────┘ └─────────────────┘ └───────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  外部 AI 客户端                             │
│     Cursor  │  VSCode  │  Claude  │  Augment  │  Cline      │
└─────────────────────────────────────────────────────────────┘
```

### 2. 核心组件

#### 2.1 工作区管理系统

- **可视化工作区编辑器**：拖拽式界面用于创建和配置工作区
- **模板系统**：针对常见用例（编程、研究、数据分析）的预配置工作区模板
- **导入/导出**：支持 yamcp 配置文件和工作区共享
- **验证引擎**：工作区配置的实时验证

#### 2.2 MCP 服务器编排

- **服务器发现**：自动检测可用的 MCP 服务器
- **配置管理**：服务器参数和环境变量的可视化编辑器
- **健康监控**：实时状态跟踪和自动重启功能
- **资源分配**：CPU 和内存监控，支持可配置限制

#### 2.3 多工作区执行引擎

- **并发处理**：多个工作区的同时执行
- **端口管理**：从 7700 开始的动态端口分配和冲突解决
- **进程隔离**：每个工作区的沙盒执行环境
- **负载均衡**：跨工作区的智能资源分配

#### 2.4 可流式传输服务接口

- **HTTP/SSE 网关**：与 supergateway 集成实现基于 Web 的访问
- **传输协议**：支持 stdio、SSE、WebSocket 和 Streamable HTTP
- **认证层**：基于令牌的安全访问认证
- **CORS 管理**：可配置的跨域资源共享

#### 2.5 AI 客户端集成中心

- **自动配置**：自动生成客户端配置文件
- **多客户端支持**：
  - Cursor (`~/.cursor/mcp.json`)
  - VSCode MCP 扩展
  - Claude Desktop
  - Augment Code
  - Cline
  - OpenCat
- **实时同步**：无需客户端重启的实时配置更新
- **连接监控**：活跃客户端连接跟踪和诊断

### 3. 技术实现策略

#### 3.1 SwiftUI 前端架构

```swift
// 核心数据模型
@Model class Workspace {
    var id: UUID
    var name: String
    var description: String
    var port: Int
    var servers: [MCPServer]
    var environment: [String: String]
    var isRunning: Bool
    var createdAt: Date
    var lastModified: Date
}

@Model class MCPServer {
    var id: UUID
    var name: String
    var namespace: String
    var type: ServerType // stdio, http, sse, streamableHttp
    var command: String
    var args: [String]
    var environment: [String: String]
    var healthCheckEndpoint: String?
    var isEnabled: Bool
}

// 进程管理
@MainActor
class ProcessManager: ObservableObject {
    @Published var runningWorkspaces: [String: WorkspaceProcess] = [:]
    @Published var systemResources: SystemResources = SystemResources()

    func startWorkspace(_ workspace: Workspace) async throws -> WorkspaceProcess
    func stopWorkspace(_ workspaceId: String) async throws
    func restartWorkspace(_ workspaceId: String) async throws
    func monitorWorkspace(_ workspaceId: String) -> AsyncStream<ProcessStatus>
}
```

#### 3.2 yamcp 集成层

```swift
class YamcpManager {
    func importServers(from configPath: String) async throws -> [MCPServer]
    func createWorkspace(_ workspace: Workspace) async throws
    func runWorkspace(_ workspaceName: String) async throws -> Process
    func listWorkspaces() async throws -> [WorkspaceInfo]
    func deleteWorkspace(_ workspaceName: String) async throws
}
```

#### 3.3 supergateway 集成

```swift
class SupergatewayManager {
    func startGateway(for workspace: Workspace) async throws -> Process
    func configureEndpoints(_ endpoints: [EndpointConfig]) async throws
    func enableCORS(origins: [String]) async throws
    func setupHealthChecks() async throws
}
```

### 4. 用户体验设计

#### 4.1 主界面

- **侧边栏导航**：工作区、服务器、客户端、设置
- **工作区仪表板**：实时状态、资源使用情况、活跃连接
- **服务器配置面板**：带语法高亮的可视化编辑器
- **客户端状态监控**：实时连接状态和配置同步

#### 4.2 系统集成

- **菜单栏应用程序**：始终可访问的系统托盘和快速操作
- **通知系统**：工作区事件的系统通知
- **键盘快捷键**：常用操作的全局热键
- **启动代理**：系统启动时自动启动

#### 4.3 无障碍功能

- **VoiceOver 支持**：完整的屏幕阅读器兼容性
- **键盘导航**：完整的纯键盘操作
- **高对比度模式**：支持无障碍显示偏好设置
- **文本缩放**：动态字体支持以提高可读性

## 开发阶段与任务分解

### 第一阶段：基础设施与核心架构（第1周）

**目标**：建立基本应用程序结构和单工作区管理

#### 任务

1. **项目设置与依赖管理**（第1天）
   - 使用 SwiftUI 和 SwiftData 初始化 Xcode 项目
   - 配置构建设置和权限
   - 设置外部工具的依赖管理
   - 创建基本应用结构和导航

2. **数据模型与持久化**（第1-2天）
   - 实现 Workspace 和 MCPServer 的 SwiftData 模型
   - 创建数据迁移策略
   - 设置 Core Data 堆栈并准备 CloudKit 同步
   - 实现基本的 CRUD 操作

3. **基础 UI 组件**（第2-3天）
   - 创建具有添加/编辑/删除功能的 WorkspaceListView
   - 实现带配置选项的 WorkspaceDetailView
   - 设计用于 MCP 服务器设置的 ServerConfigurationView
   - 添加基本表单验证和错误处理

4. **yamcp CLI 集成**（第3-4天）
   - 实现用于 CLI 操作的 YamcpManager 类
   - 创建带 async/await 的进程执行包装器
   - 添加错误处理和输出解析
   - 测试基本工作区创建和服务器导入

5. **单工作区执行**（第4-5天）
   - 为单工作区实现基本的 ProcessManager
   - 添加带进程监控的启动/停止功能
   - 创建简单的状态显示和日志记录
   - 测试端到端工作区执行

**交付成果**：

- 具有工作区管理功能的 SwiftUI 应用
- 基本操作的 yamcp 集成
- 单工作区执行能力
- 基本数据持久化和 UI

### 第二阶段：多工作区编排（第2周）

**目标**：启用并发工作区执行和资源管理

#### 任务

1. **并发进程管理**（第6-7天）
   - 扩展 ProcessManager 以支持多个同时运行的工作区
   - 实现进程隔离和资源分配
   - 添加进程生命周期管理（启动、停止、重启、崩溃恢复）
   - 创建带健康检查的进程监控

2. **端口管理系统**（第7-8天）
   - 实现从 7700 开始的动态端口分配
   - 添加端口冲突检测和解决
   - 创建端口预留和清理机制
   - 测试并发工作区端口分配

3. **supergateway 集成**（第8-9天）
   - 集成 supergateway 用于 HTTP/SSE 端点创建
   - 实现传输协议选择（stdio、SSE、WebSocket）
   - 添加端点配置和管理
   - 创建网关进程监控

4. **资源监控基础**（第9-10天）
   - 实现每个工作区的基本 CPU 和内存监控
   - 添加系统资源跟踪和警报
   - 创建资源使用可视化组件
   - 设置性能指标收集

**交付成果**：

- 多工作区并发执行
- 动态端口管理系统
- 带 HTTP/SSE 端点的 supergateway 集成
- 基本资源监控能力

### 第三阶段：高级监控与日志（第3周）

**目标**：全面监控、日志记录和错误处理

#### 任务

1. **增强资源监控**（第11-12天）
   - 实现详细的 CPU、内存和网络监控
   - 添加子进程的进程树跟踪
   - 创建资源使用警报和阈值
   - 实现自动资源清理

2. **集中式日志系统**（第12-13天）
   - 为所有工作区进程创建统一日志
   - 实现日志轮转和归档
   - 添加日志过滤和搜索功能
   - 创建日志导出功能（JSON、CSV、纯文本）

3. **错误处理与恢复**（第13-14天）
   - 为所有进程操作实现全面的错误处理
   - 添加带退避策略的自动重启机制
   - 创建错误报告和诊断
   - 实现优雅关闭程序

4. **性能优化**（第14-15天）
   - 优化进程管理以获得更好的性能
   - 为工作区组件实现延迟加载
   - 为频繁访问的数据添加缓存
   - 分析和优化内存使用

**交付成果**：

- 全面的资源监控仪表板
- 带搜索和导出功能的集中式日志
- 强大的错误处理和恢复机制
- 性能优化的进程管理

### 第四阶段：AI 客户端集成（第4周）

**目标**：与多个 AI 编程客户端的无缝集成

#### 任务

1. **配置模板系统**（第16-17天）
   - 为支持的 AI 客户端创建配置模板
   - 实现带动态值的模板渲染
   - 添加模板验证和测试
   - 创建自定义模板创建界面

2. **客户端配置管理**（第17-18天）
   - 实现自动配置文件写入
   - 添加对 Cursor、VSCode、Claude Desktop、Augment、Cline 的支持
   - 创建配置备份和恢复功能
   - 实现配置验证和测试

3. **实时配置同步**（第18-19天）
   - 实现无需客户端重启的实时配置更新
   - 添加配置更改的文件系统监控
   - 创建配置冲突解决
   - 测试各种客户端的热重载功能

4. **连接监控与诊断**（第19-20天）
   - 实现活跃客户端连接跟踪
   - 添加连接诊断和故障排除工具
   - 创建客户端特定的优化设置
   - 实现连接健康监控

**交付成果**：

- 所有支持的 AI 客户端的自动配置生成
- 实时配置同步
- 全面的连接监控和诊断
- 客户端特定的优化和故障排除工具

### 第五阶段：系统集成与完善（第5周）

**目标**：完成 macOS 系统集成和用户体验完善

#### 任务

1. **macOS 系统集成**（第21-22天）
   - 实现 NSStatusBar（系统托盘）功能
   - 创建自动启动的启动代理
   - 添加用于安全凭据存储的 Keychain 集成
   - 实现系统通知支持

2. **高级 UI/UX 功能**（第22-23天）
   - 添加键盘快捷键和全局热键
   - 实现窗口管理和偏好设置
   - 创建高级工作区模板和预设
   - 为配置添加拖放功能

3. **安全性与沙盒**（第23-24天）
   - 实现 App Sandbox 合规性
   - 添加安全进程执行
   - 创建文件系统访问的权限管理
   - 实现安全凭据处理

4. **测试与质量保证**（第24-25天）
   - 所有功能的全面测试
   - 性能测试和优化
   - 用户验收测试和反馈集成
   - 最终错误修复和完善

**交付成果**：

- 完整的 macOS 系统集成
- 具有高级功能的精美用户界面
- 符合安全标准的沙盒应用程序
- 经过全面测试和优化的最终产品

## 技术规格

### 系统要求

- **macOS**：13.0（Ventura）或更高版本
- **架构**：通用二进制文件（Intel + Apple Silicon）
- **依赖项**：Node.js（用于 yamcp 和 supergateway）
- **权限**：网络访问、配置文件的文件系统访问

### 性能目标

- **启动时间**：冷启动 < 2 秒
- **内存使用**：基础 < 100MB，每个活跃工作区 +20MB
- **CPU 使用**：空闲 < 5%，每个活跃工作区 < 20%
- **并发工作区**：支持 10+ 个同时运行的工作区

### 安全考虑

- **App Sandbox**：完全符合 macOS App Sandbox
- **代码签名**：使用 Developer ID 签名进行分发
- **网络安全**：所有外部通信使用 TLS 加密
- **凭据存储**：敏感数据的 Keychain 集成

### 部署策略

- **分发**：Mac App Store + 直接下载
- **更新**：带用户同意的自动更新机制
- **遥测**：可选的匿名使用分析
- **支持**：应用内帮助系统和文档

## Risk Assessment & Mitigation

### Technical Risks

1. **Process Management Complexity**
   - *Risk*: Difficulty managing multiple concurrent processes
   - *Mitigation*: Use proven Swift Process patterns and extensive testing

2. **Port Conflicts**
   - *Risk*: Port allocation conflicts between workspaces
   - *Mitigation*: Implement robust dynamic port allocation with conflict detection

3. **Configuration Complexity**
   - *Risk*: Managing diverse client configuration formats
   - *Mitigation*: Use template-based approach with validation

4. **System Integration Issues**
   - *Risk*: macOS sandboxing and permission challenges
   - *Mitigation*: Follow Apple guidelines and implement proper entitlements

### Business Risks

1. **User Adoption**
   - *Risk*: Complex interface may deter users
   - *Mitigation*: Focus on intuitive design and comprehensive onboarding

2. **Maintenance Overhead**
   - *Risk*: Supporting multiple AI client integrations
   - *Mitigation*: Modular architecture with plugin-like client support

## Success Metrics

### Technical Metrics

- **Stability**: < 1% crash rate in production
- **Performance**: 95% of operations complete within 2 seconds
- **Compatibility**: Support for 95% of common MCP server configurations
- **Resource Efficiency**: < 200MB total memory usage with 5 active workspaces

### User Experience Metrics

- **Onboarding**: 80% of users successfully create first workspace within 5 minutes
- **Daily Usage**: Average 3+ workspace operations per active user per day
- **Client Integration**: 90% success rate for automatic client configuration
- **User Satisfaction**: 4.5+ star rating on Mac App Store

This comprehensive development plan provides a roadmap for creating a robust, user-friendly macOS native application that effectively manages yamcp workspaces while providing seamless integration with popular AI coding clients.
