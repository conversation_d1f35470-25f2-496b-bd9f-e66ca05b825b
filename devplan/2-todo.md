# MCP Copilot - macOS 原生客户端开发计划

## 项目概述

本文档概述了 **MCP Copilot** 的开发计划，这是一个 macOS 原生 SwiftUI 应用程序，作为 yamcp（Yet Another MCP）工作区的管理层。该应用程序能够同时管理和执行多个 MCP 服务器工作区，同时为 AI 客户端集成提供可流式传输的 HTTP/SSE 接口。

## 功能设计文档（基于 umate.ai 界面优化）

### 1. 核心架构与界面设计理念

参考 umate.ai 的优秀设计，我们采用左侧导航 + 右侧详情的布局模式：

```
┌─────────────────────────────────────────────────────────────┐
│  ┌─────────────┐ │                主要内容区域               │
│  │ 侧边栏导航   │ │  ┌─────────────────────────────────────┐ │
│  │ Dashboard   │ │  │          客户端应用管理              │ │
│  │ Client Apps │ │  │  ┌─────────┐ ┌─────────┐ ┌─────────┐ │ │
│  │ Config Suits│ │  │  │ Claude  │ │ Cursor  │ │Windsurf │ │ │
│  │ MCP Servers │ │  │  │Desktop  │ │         │ │         │ │ │
│  │ Tools       │ │  │  └─────────┘ └─────────┘ └─────────┘ │ │
│  │ Prompts     │ │  └─────────────────────────────────────┘ │
│  │ Resources   │ │                                          │
│  │ Logs        │ │  ┌─────────────────────────────────────┐ │
│  │ Settings    │ │  │        配置套件管理                  │ │
│  │ About       │ │  │  ○ Coding (默认)                    │ │
│  └─────────────┘ │  │  ○ Research                         │ │
│                  │  │  ○ Data Analysis                    │ │
│                  │  └─────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. 基于 umate.ai 的界面功能重新设计

#### 2.1 客户端应用管理（Client Apps）- 核心功能

参考 umate.ai 的客户端管理界面，重新设计为以客户端为中心的管理模式：

**主要功能**：

- **客户端检测与状态显示**：
  - 自动检测已安装的 AI 客户端（Claude Desktop、Cursor、Windsurf、Zed 等）
  - 实时显示连接状态：Detected（已检测）、Enabled（已启用）、Disabled（已禁用）
  - 支持手动重新检测和状态刷新

- **一键配置管理**：
  - **Redetect**：重新扫描系统中的客户端应用
  - **Disabled/Enabled**：快速启用/禁用特定客户端的 MCP 连接
  - **Manual Config**：手动配置客户端连接参数

- **配置套件系统（Config Suits）**：
  - **预设配置套件**：Coding、Research、Data Analysis 等场景
  - **自定义配置**：支持用户创建自定义配置套件
  - **一键切换**：快速在不同配置套件间切换
  - **配置共享**：支持导入/导出配置套件

#### 2.2 MCP 服务器管理（MCP Servers）

基于 yamcp 的服务器管理，但界面更加直观：

- **服务器库管理**：
  - 可视化的服务器添加/编辑界面
  - 支持从 yamcp 配置导入服务器
  - 服务器分类和标签管理
  - 健康状态监控和自动重启

- **场景工作区管理**：
  - 每个场景包含多个 MCP 服务器的组合
  - 场景模板和快速创建
  - 场景间的服务器共享和复用
  - 场景启动/停止的批量操作

#### 2.3 工具与资源管理（Tools & Resources）

- **工具集成**：
  - 内置常用 MCP 工具的快速访问
  - 工具使用统计和推荐
  - 自定义工具添加和管理

- **提示词管理（Prompts）**：
  - 场景相关的提示词模板
  - 提示词版本控制和分享
  - 与客户端的提示词同步

- **资源监控**：
  - 系统资源使用情况
  - 各客户端连接状态
  - 性能指标和优化建议

#### 2.4 配置套件详细设计

参考 umate.ai 的 "Available Configuration Suits" 设计：

```
配置套件选择界面：
┌─────────────────────────────────────────────────────────┐
│ Config by Suit ▼                                       │
│ Choose a pre-configured suit that matches your needs   │
│                                                         │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│ │Configure    │ │Custom (WIP) │ │   Manual    │        │
│ │   Suit      │ │             │ │             │        │
│ └─────────────┘ └─────────────┘ └─────────────┘        │
│                                                         │
│ Available Configuration Suits                           │
│ Select a pre-configured suit for Claude Desktop        │
│                                                         │
│ ○ Coding                                               │
│   No description                                        │
│   Scenario                                             │
│                                                         │
│ ● default    [DEFAULT] [ACTIVE]                        │
│   Default configuration suit                           │
│   Shared                                               │
└─────────────────────────────────────────────────────────┘
```

**配置套件功能**：

- **预设套件**：
  - **Coding**：包含代码相关的 MCP 服务器（文件系统、Git、代码分析等）
  - **Research**：包含搜索、文档处理、知识管理相关服务器
  - **Data Analysis**：包含数据处理、可视化、统计分析服务器
  - **Writing**：包含文档编辑、语法检查、翻译服务器

- **套件状态标识**：
  - **DEFAULT**：默认套件标识
  - **ACTIVE**：当前激活状态
  - **SHARED**：可共享套件
  - **CUSTOM**：用户自定义套件

### 3. 基于新界面设计的技术实现策略

#### 3.1 SwiftUI 前端架构（重新设计）

基于 umate.ai 的界面设计，重新构建数据模型：

```swift
// 客户端应用模型
@Model class ClientApp {
    var id: UUID
    var name: String // "Claude Desktop", "Cursor", "Windsurf", "Zed"
    var bundleId: String // 应用包标识符
    var configPath: String // 配置文件路径
    var isDetected: Bool // 是否检测到
    var isEnabled: Bool // 是否启用
    var connectionStatus: ConnectionStatus // 连接状态
    var lastConnected: Date?
    var icon: String // 应用图标
}

// 配置套件模型
@Model class ConfigSuit {
    var id: UUID
    var name: String // "Coding", "Research", "Data Analysis"
    var description: String
    var isDefault: Bool
    var isActive: Bool
    var isShared: Bool
    var servers: [MCPServer] // 包含的服务器列表
    var clientConfigs: [String: ClientConfig] // 每个客户端的特定配置
    var createdAt: Date
    var lastModified: Date
}

// MCP 服务器模型（简化）
@Model class MCPServer {
    var id: UUID
    var name: String
    var namespace: String
    var type: ServerType // stdio, http, sse
    var command: String
    var args: [String]
    var environment: [String: String]
    var isEnabled: Bool
    var healthStatus: HealthStatus
    var category: ServerCategory // 服务器分类
    var tags: [String] // 标签
}

// 主要管理器
@MainActor
class ClientAppManager: ObservableObject {
    @Published var detectedApps: [ClientApp] = []
    @Published var configSuits: [ConfigSuit] = []
    @Published var activeConfigSuit: ConfigSuit?

    func detectInstalledApps() async
    func enableApp(_ app: ClientApp, with suit: ConfigSuit) async throws
    func disableApp(_ app: ClientApp) async throws
    func switchConfigSuit(_ suit: ConfigSuit) async throws
    func redetectApps() async
}
```

#### 3.2 界面组件设计

基于 umate.ai 的布局，设计主要 SwiftUI 组件：

```swift
// 主界面结构
struct ContentView: View {
    @StateObject private var clientManager = ClientAppManager()
    @State private var selectedNavItem: NavigationItem = .clientApps

    var body: some View {
        NavigationSplitView {
            SidebarView(selectedItem: $selectedNavItem)
        } detail: {
            switch selectedNavItem {
            case .dashboard:
                DashboardView()
            case .clientApps:
                ClientAppsView()
            case .configSuits:
                ConfigSuitsView()
            case .mcpServers:
                MCPServersView()
            case .tools:
                ToolsView()
            case .prompts:
                PromptsView()
            case .resources:
                ResourcesView()
            case .logs:
                LogsView()
            case .settings:
                SettingsView()
            }
        }
    }
}

// 客户端应用管理界面
struct ClientAppsView: View {
    @EnvironmentObject var clientManager: ClientAppManager
    @State private var selectedApp: ClientApp?

    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 顶部搜索和过滤
            HStack {
                SearchField(text: $searchText, placeholder: "Search apps...")
                Picker("Status", selection: $statusFilter) {
                    Text("All").tag(StatusFilter.all)
                    Text("Detected").tag(StatusFilter.detected)
                    Text("Enabled").tag(StatusFilter.enabled)
                }
            }

            // 客户端应用网格
            LazyVGrid(columns: gridColumns, spacing: 16) {
                ForEach(filteredApps) { app in
                    ClientAppCard(app: app, isSelected: selectedApp?.id == app.id)
                        .onTapGesture {
                            selectedApp = app
                        }
                }
            }

            // 右侧详情面板
            if let selectedApp = selectedApp {
                ClientAppDetailView(app: selectedApp)
            }
        }
        .padding()
    }
}
```

#### 3.3 yamcp 集成层（优化）

针对新的界面设计，优化 yamcp 集成：

```swift
class YamcpManager {
    // 配置套件相关
    func createConfigSuit(_ suit: ConfigSuit) async throws
    func activateConfigSuit(_ suit: ConfigSuit) async throws
    func exportConfigSuit(_ suit: ConfigSuit) async throws -> URL
    func importConfigSuit(from url: URL) async throws -> ConfigSuit

    // 客户端配置相关
    func generateClientConfig(for app: ClientApp, with suit: ConfigSuit) async throws -> ClientConfig
    func writeClientConfig(_ config: ClientConfig, to app: ClientApp) async throws
    func validateClientConfig(_ config: ClientConfig) async throws -> Bool

    // 服务器管理
    func importServers(from configPath: String) async throws -> [MCPServer]
    func startServersForSuit(_ suit: ConfigSuit) async throws
    func stopServersForSuit(_ suit: ConfigSuit) async throws
    func monitorServerHealth(_ server: MCPServer) -> AsyncStream<HealthStatus>
}
```

### 4. 基于 umate.ai 的用户体验设计

#### 4.1 主界面布局（参考 umate.ai）

**左侧导航栏**：

- **Dashboard**：总览仪表板，显示系统状态和关键指标
- **Client Apps**：客户端应用管理（核心功能）
- **Config Suits**：配置套件管理
- **MCP Servers**：服务器库管理
- **Tools**：工具集成和管理
- **Prompts**：提示词模板管理
- **Resources**：资源监控和管理
- **Logs**：日志查看和分析
- **Settings**：系统设置
- **About**：关于和帮助信息

**主内容区域**：

- **客户端卡片展示**：类似 umate.ai 的应用卡片布局
- **状态指示器**：清晰的视觉状态反馈（绿色圆点表示已检测，启用/禁用切换）
- **快速操作按钮**：Redetect、Enable/Disable、Manual Config
- **详情面板**：右侧滑出式详情配置面板

#### 4.2 交互设计优化

**一键操作流程**：

1. **检测客户端**：应用启动时自动检测，支持手动重新检测
2. **选择配置套件**：从预设套件中选择或创建自定义套件
3. **启用客户端**：一键启用特定客户端的 MCP 连接
4. **实时监控**：显示连接状态和性能指标

**配置套件切换**：

- **快速切换**：在不同场景间快速切换配置
- **预览模式**：切换前预览配置变更
- **回滚功能**：支持配置回滚到之前状态
- **批量操作**：支持多个客户端的批量配置

#### 4.3 易用性改进

**简化的配置流程**：

- **智能检测**：自动检测客户端安装路径和配置文件位置
- **模板化配置**：提供常见场景的配置模板
- **可视化编辑**：图形化的配置编辑界面，减少手动编辑
- **实时验证**：配置修改时的实时验证和错误提示

**状态可视化**：

- **连接状态指示**：清晰的视觉状态指示器
- **性能监控**：实时的资源使用情况显示
- **错误提示**：友好的错误信息和解决建议
- **操作反馈**：操作成功/失败的即时反馈

#### 4.4 系统集成（保持原有功能）

- **菜单栏应用程序**：始终可访问的系统托盘和快速操作
- **通知系统**：客户端连接状态变化的系统通知
- **键盘快捷键**：常用操作的全局热键
- **启动代理**：系统启动时自动启动和配置恢复

## 开发阶段与任务分解

### 第一阶段：基础架构与客户端检测（第1周）

**目标**：建立基于 umate.ai 设计的应用程序结构和客户端检测功能

#### 第一阶段任务

1. **项目设置与界面框架**（第1天）
   - 使用 SwiftUI 和 SwiftData 初始化 Xcode 项目
   - 实现左侧导航栏 + 右侧内容区域的布局结构
   - 创建基本的导航组件和路由系统
   - 设置应用图标和基本样式

2. **数据模型设计**（第1-2天）
   - 实现 ClientApp、ConfigSuit、MCPServer 的 SwiftData 模型
   - 创建客户端检测和状态管理的数据结构
   - 设置配置套件的数据关系和约束
   - 实现基本的数据持久化和迁移策略

3. **客户端检测系统**（第2-3天）
   - 实现 macOS 应用程序自动检测功能
   - 创建 ClientAppManager 用于管理客户端状态
   - 添加常见 AI 客户端的检测规则（Claude Desktop、Cursor、Windsurf、Zed）
   - 实现客户端配置文件路径的自动发现

4. **基础 UI 组件**（第3-4天）
   - 创建 ClientAppsView 主界面（参考 umate.ai 布局）
   - 实现客户端应用卡片组件（ClientAppCard）
   - 添加状态指示器和快速操作按钮
   - 创建搜索和过滤功能

5. **yamcp 基础集成**（第4-5天）
   - 实现 YamcpManager 的基础功能
   - 创建配置套件的导入/导出功能
   - 添加基本的服务器管理操作
   - 测试客户端检测和基础配置生成

**交付成果**：

- 完整的应用程序界面框架（基于 umate.ai 设计）
- 客户端自动检测和状态管理功能
- 基础的配置套件系统
- yamcp 集成的基础功能

### 第二阶段：配置套件与一键配置（第2周）

**目标**：实现配置套件系统和一键客户端配置功能

#### 第二阶段任务

1. **配置套件系统开发**（第6-7天）
   - 实现配置套件的创建、编辑、删除功能
   - 创建预设配置套件（Coding、Research、Data Analysis、Writing）
   - 添加配置套件的导入/导出功能
   - 实现配置套件间的快速切换机制

2. **客户端配置生成**（第7-8天）
   - 为每个支持的客户端创建配置模板
   - 实现动态配置文件生成功能
   - 添加配置文件的自动写入和备份
   - 创建配置验证和测试机制

3. **一键启用/禁用功能**（第8-9天）
   - 实现客户端的一键启用/禁用功能
   - 添加批量客户端配置操作
   - 创建配置状态的实时同步
   - 实现配置冲突检测和解决

4. **supergateway 集成**（第9-10天）
   - 集成 supergateway 用于统一的 HTTP/SSE 接口
   - 实现基于配置套件的端点管理
   - 添加动态端口分配和管理
   - 创建服务健康检查和监控

**交付成果**：

- 完整的配置套件管理系统
- 一键客户端配置功能
- 多客户端批量操作支持
- supergateway 集成的统一接口

### 第三阶段：MCP 服务器管理与工具集成（第3周）

**目标**：完善 MCP 服务器管理和工具生态系统

#### 第三阶段任务

1. **MCP 服务器库管理**（第11-12天）
   - 实现服务器的可视化添加、编辑、删除功能
   - 创建服务器分类和标签系统
   - 添加服务器健康状态监控
   - 实现服务器配置的导入/导出

2. **工具集成系统**（第12-13天）
   - 创建 Tools 页面和工具管理功能
   - 集成常用 MCP 工具的快速访问
   - 添加工具使用统计和推荐系统
   - 实现自定义工具的添加和管理

3. **提示词管理系统**（第13-14天）
   - 实现 Prompts 页面和提示词模板管理
   - 创建场景相关的提示词分类
   - 添加提示词的版本控制和分享功能
   - 实现与客户端的提示词同步

4. **资源监控与日志**（第14-15天）
   - 创建 Resources 页面显示系统资源使用情况
   - 实现各客户端连接状态的实时监控
   - 添加性能指标收集和分析
   - 创建 Logs 页面用于日志查看和分析

**交付成果**：

- 完整的 MCP 服务器管理系统
- 工具集成和管理功能
- 提示词模板管理系统
- 资源监控和日志分析功能

### 第四阶段：高级功能与用户体验优化（第4周）

**目标**：实现高级功能和用户体验优化

#### 第四阶段任务

1. **Dashboard 仪表板开发**（第16-17天）
   - 创建系统总览仪表板
   - 实现关键指标的可视化展示
   - 添加快速操作和状态概览
   - 创建个性化的仪表板配置

2. **高级配置功能**（第17-18天）
   - 实现 Manual Config 手动配置功能
   - 添加高级配置选项和专家模式
   - 创建配置预览和差异对比功能
   - 实现配置模板的自定义创建

3. **实时同步与热重载**（第18-19天）
   - 实现配置的实时同步和热重载
   - 添加文件系统监控和自动更新
   - 创建配置冲突检测和解决机制
   - 测试各客户端的无缝配置更新

4. **用户体验优化**（第19-20天）
   - 优化界面响应性和交互体验
   - 添加操作反馈和进度指示
   - 实现键盘快捷键和无障碍功能
   - 创建用户引导和帮助系统

**交付成果**：

- 功能完整的 Dashboard 仪表板
- 高级配置和专家模式功能
- 实时同步和热重载机制
- 优化的用户体验和交互设计

### 第五阶段：系统集成与发布准备（第5周）

**目标**：完成 macOS 系统集成和发布准备

#### 第五阶段任务

1. **macOS 系统深度集成**（第21-22天）
   - 实现 NSStatusBar（系统托盘）功能和快速操作
   - 创建 LaunchAgent 自动启动机制
   - 添加 Keychain 集成用于安全凭据存储
   - 实现系统通知和状态更新

2. **Settings 和 About 页面**（第22-23天）
   - 创建完整的 Settings 设置页面
   - 实现应用偏好设置和个性化选项
   - 添加 About 页面和帮助文档
   - 创建用户反馈和支持系统

3. **安全性与性能优化**（第23-24天）
   - 实现 App Sandbox 合规性
   - 优化应用性能和内存使用
   - 添加错误报告和崩溃分析
   - 实现安全的进程管理和权限控制

4. **测试、打包与发布准备**（第24-25天）
   - 全面的功能测试和用户体验测试
   - 应用签名和公证准备
   - 创建安装包和分发准备
   - 文档完善和用户指南编写

**交付成果**：

- 完整的 macOS 原生应用程序
- 系统级集成和托盘功能
- 安全合规的应用程序包
- 可发布的最终产品和文档

## 基于 umate.ai 设计的核心优势

### 1. 用户体验优势

- **直观的界面设计**：采用 umate.ai 的成熟界面布局，降低学习成本
- **一键操作流程**：简化复杂的 MCP 配置过程，提供一键启用/禁用功能
- **可视化状态管理**：清晰的状态指示器和实时反馈
- **配置套件系统**：预设场景配置，快速切换不同工作模式

### 2. 技术架构优势

- **基于 yamcp 的稳定后端**：利用成熟的 yamcp 工具作为核心引擎
- **统一的 streamable 接口**：通过 supergateway 提供统一的 HTTP/SSE 接口
- **模块化设计**：清晰的功能模块划分，便于维护和扩展
- **原生 macOS 集成**：充分利用 macOS 系统特性和用户习惯

### 3. 易用性改进

- **自动检测机制**：自动发现已安装的 AI 客户端
- **智能配置生成**：基于场景自动生成最优配置
- **批量操作支持**：支持多客户端的批量配置管理
- **实时同步更新**：配置变更的实时同步，无需手动重启

这个重新设计的开发计划充分借鉴了 umate.ai 的优秀界面设计，同时保持了基于 yamcp 的技术架构，为用户提供了更加直观、易用的 MCP 管理体验。

## 技术规格

### 系统要求

- **macOS**：13.0（Ventura）或更高版本
- **架构**：通用二进制文件（Intel + Apple Silicon）
- **依赖项**：Node.js（用于 yamcp 和 supergateway）
- **权限**：网络访问、配置文件的文件系统访问

### 性能目标

- **启动时间**：冷启动 < 2 秒
- **内存使用**：基础 < 100MB，每个活跃工作区 +20MB
- **CPU 使用**：空闲 < 5%，每个活跃工作区 < 20%
- **并发工作区**：支持 10+ 个同时运行的工作区

### 安全考虑

- **App Sandbox**：完全符合 macOS App Sandbox
- **代码签名**：使用 Developer ID 签名进行分发
- **网络安全**：所有外部通信使用 TLS 加密
- **凭据存储**：敏感数据的 Keychain 集成

### 部署策略

- **分发**：Mac App Store + 直接下载
- **更新**：带用户同意的自动更新机制
- **遥测**：可选的匿名使用分析
- **支持**：应用内帮助系统和文档

## 风险评估与缓解

### 技术风险

1. **进程管理复杂性**
   - *风险*：管理多个并发进程的难度
   - *缓解措施*：使用经过验证的 Swift Process 模式和广泛测试

2. **端口冲突**
   - *风险*：工作区之间的端口分配冲突
   - *缓解措施*：实现带冲突检测的强大动态端口分配

3. **配置复杂性**
   - *风险*：管理多样化的客户端配置格式
   - *缓解措施*：使用带验证的基于模板的方法

4. **系统集成问题**
   - *风险*：macOS 沙盒和权限挑战
   - *缓解措施*：遵循 Apple 指南并实现适当的权限

### 业务风险

1. **用户采用**
   - *风险*：复杂界面可能阻止用户使用
   - *缓解措施*：专注于直观设计和全面的入门指导

2. **维护开销**
   - *风险*：支持多个 AI 客户端集成
   - *缓解措施*：采用模块化架构和类似插件的客户端支持

## 成功指标

### 技术指标

- **稳定性**：生产环境中崩溃率 < 1%
- **性能**：95% 的操作在 2 秒内完成
- **兼容性**：支持 95% 的常见 MCP 服务器配置
- **资源效率**：5 个活跃工作区的总内存使用 < 200MB

### 用户体验指标

- **入门体验**：80% 的用户在 5 分钟内成功创建第一个工作区
- **日常使用**：每个活跃用户每天平均进行 3+ 次工作区操作
- **客户端集成**：自动客户端配置的成功率为 90%
- **用户满意度**：Mac App Store 上 4.5+ 星评级

这个全面的开发计划为创建一个强大、用户友好的 macOS 原生应用程序提供了路线图，该应用程序能够有效管理 yamcp 工作区，同时与流行的 AI 编程客户端提供无缝集成。
